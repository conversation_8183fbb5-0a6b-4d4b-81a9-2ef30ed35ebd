import pytorch_lightning as pl
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any, List
from models.decoder import build_decoder
from models.loss import GraspLossPose
from models.utils.diffusion_utils import make_schedule_ddpm
from utils.hand_helper import process_hand_pose, process_hand_pose_test, denorm_hand_pose_robust
from statistics import mean
import logging
from .utils.log_colors import HEADER, BLUE, GREEN, YELLOW, RED, ENDC, BOLD, UNDERLINE
from .utils.diffusion_core import DiffusionCoreMixin
from .utils.prediction import build_pred_dict_adaptive
from .utils.logging_helpers import log_validation_summary, convert_number_to_emoji

class DDPMLightning(DiffusionCoreMixin, pl.LightningModule):
    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning model{ENDC}")
        self.save_hyperparameters()
        
        self.use_negative_prompts = cfg.get('use_negative_prompts', True)
        
        self.eps_model = build_decoder(cfg.decoder)
        self.criterion = GraspLossPose(cfg.criterion)
        self.loss_weights = cfg.criterion.loss_weights
        self.rot_type = cfg.rot_type
        self.batch_size = cfg.batch_size
        self.print_freq = cfg.print_freq
        self.use_score = cfg.use_score
        self.score_pretrain = cfg.score_pretrain
        
        self.timesteps = cfg.steps
        self.schedule_cfg = cfg.schedule_cfg
        self.rand_t_type = cfg.rand_t_type
        self.pred_x0 = cfg.pred_x0
        self.mode = cfg.mode
        
        for k, v in make_schedule_ddpm(self.timesteps, **self.schedule_cfg).items():
            self.register_buffer(k, v)
            
        self.optimizer_cfg = cfg.optimizer
        self.scheduler = cfg.scheduler

        self.use_cfg = cfg.use_cfg
        self.guidance_scale = cfg.guidance_scale
        self.use_negative_guidance = cfg.use_negative_guidance and self.use_negative_prompts
        self.negative_guidance_scale = cfg.negative_guidance_scale

        # WandB优化配置参数（从配置中获取，避免在训练时访问trainer）
        wandb_opt = cfg.get('wandb_optimization', {})
        self._log_gradients = wandb_opt.get('log_gradients', False)
        self._gradient_freq = wandb_opt.get('gradient_freq', 1000)
        self._monitor_system = wandb_opt.get('monitor_system', False)
        self._system_freq = wandb_opt.get('system_freq', 500)

    # =====================
    # PyTorch Lightning Hooks
    # =====================

    def forward_train(self, batch):
        """Legacy training forward interface."""
        loss, loss_dict, _ = self._compute_loss(batch, mode='train')
        return loss, loss_dict

    def training_step(self, batch, batch_idx):
        """Training step: supports single and multi-grasp parallel training."""
        loss, loss_dict, processed_batch = self._compute_loss(batch, mode='train')

        # Calculate total samples based on processed batch
        norm_pose = processed_batch['norm_pose']
        if norm_pose.dim() == 3:
            B, num_grasps, _ = norm_pose.shape
            total_samples = B * num_grasps
        else:
            total_samples = norm_pose.shape[0]

        # 记录训练损失 - 使用train/前缀便于wandb组织
        train_log_dict = {}
        for k, v in loss_dict.items():
            train_log_dict[f"train/{k}"] = v
        train_log_dict["train/total_loss"] = loss

        self.log_dict(train_log_dict, prog_bar=False, logger=True, on_step=True, on_epoch=True, batch_size=total_samples)

        # 记录学习率和优化器信息
        optimizer = self.optimizers()
        self.log("train/lr", optimizer.param_groups[0]['lr'], prog_bar=False, logger=True, on_step=True, batch_size=total_samples)

        # 记录梯度信息（根据配置决定是否记录，减少流量消耗）
        # 使用更安全的方式获取配置，避免在分布式训练中出现问题
        log_gradients = getattr(self, '_log_gradients', False)
        gradient_freq = getattr(self, '_gradient_freq', 1000)

        if log_gradients and batch_idx % gradient_freq == 0:
            total_norm = 0
            param_count = 0
            for p in self.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1
            if param_count > 0:
                total_norm = total_norm ** (1. / 2)
                self.log("train/grad_norm", total_norm, prog_bar=False, logger=True, on_step=True, batch_size=total_samples)

        # 记录系统资源信息（根据配置决定频率，减少流量消耗）
        # 使用更安全的方式获取配置
        monitor_system = getattr(self, '_monitor_system', False)
        system_freq = getattr(self, '_system_freq', 500)

        if monitor_system and batch_idx % system_freq == 0:
            # GPU内存使用情况
            if torch.cuda.is_available():
                gpu_memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                gpu_memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
                self.log("system/gpu_memory_allocated_gb", gpu_memory_allocated, prog_bar=False, logger=True, on_step=True, batch_size=total_samples)
                self.log("system/gpu_memory_reserved_gb", gpu_memory_reserved, prog_bar=False, logger=True, on_step=True, batch_size=total_samples)

            # 训练速度信息
            if hasattr(self.trainer, 'fit_loop') and hasattr(self.trainer.fit_loop, 'epoch_loop'):
                # 计算每秒处理的样本数
                if hasattr(self, '_last_log_time'):
                    import time
                    current_time = time.time()
                    time_diff = current_time - self._last_log_time
                    if time_diff > 0:
                        samples_per_sec = total_samples / time_diff
                        self.log("system/samples_per_sec", samples_per_sec, prog_bar=False, logger=True, on_step=True, batch_size=total_samples)

                import time
                self._last_log_time = time.time()

        # 只在主进程打印详细日志（PyTorch Lightning会自动处理分布式训练）
        if batch_idx % self.print_freq == 0 and self.trainer.is_global_zero:
            empty_formatter = logging.Formatter('')
            root_logger = logging.getLogger()
            original_formatters = [handler.formatter for handler in root_logger.handlers]

            for handler in root_logger.handlers:
                handler.setFormatter(empty_formatter)
            logging.info("")

            for handler, formatter in zip(root_logger.handlers, original_formatters):
                handler.setFormatter(formatter)

            logging.info(f'{HEADER}Epoch {self.current_epoch} - Batch [{batch_idx}/{len(self.trainer.train_dataloader)}]{ENDC}')
            logging.info(f'{GREEN}{"Loss:":<21s} {loss.item():.4f}{ENDC}')
            for k, v in loss_dict.items():
                logging.info(f'{BLUE}{k.title() + ":":<21s} {v.item():.4f}{ENDC}')

        return loss

    def on_validation_epoch_start(self):
        self.validation_step_outputs = []
        return

    def validation_step(self, batch, batch_idx):
        """Validation step: supports multi-grasp parallel inference."""
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        
        pred_x0 = self.sample(batch)[:, 0, -1]

        # Ensure correct prediction dimensions, especially for batch_size=1
        if batch['norm_pose'].dim() == 2 and pred_x0.dim() == 1:
            pred_x0 = pred_x0.unsqueeze(0)
        if batch['norm_pose'].dim() == 3 and pred_x0.dim() == 2:
            pred_x0 = pred_x0.unsqueeze(1)
        
        pred_dict = build_pred_dict_adaptive(pred_x0)

        # Determine batch size for logging
        if pred_x0.dim() == 3:
            batch_size = pred_x0.shape[0] * pred_x0.shape[1]
        else:
            batch_size = pred_x0.shape[0]

        # Loss computation
        loss_dict = self.criterion(pred_dict, batch, mode='val')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        # Logging with correct batch_size
        self.log("val/loss", loss, prog_bar=False, batch_size=batch_size, sync_dist=True)
        self.validation_step_outputs.append({
            "loss": loss.item(),
            "loss_dict": {k: v.item() for k, v in loss_dict.items()}
        })

        return {"loss": loss, "loss_dict": loss_dict}
        
    def on_validation_epoch_end(self):
        val_loss = [x["loss"] for x in self.validation_step_outputs]
        avg_loss = mean(val_loss)
        
        val_detailed_loss = {}
        for k in self.validation_step_outputs[0]["loss_dict"].keys():
            val_detailed_loss[k] = mean([x["loss_dict"][k] for x in self.validation_step_outputs])
        
        # Calculate additional statistics
        num_batches = len(self.validation_step_outputs)
        loss_std = torch.std(torch.tensor(val_loss)).item() if len(val_loss) > 1 else 0.0
        loss_min = min(val_loss)
        loss_max = max(val_loss)
        
        # Console colored logging
        log_validation_summary(epoch=self.current_epoch,
                             num_batches=num_batches,
                             avg_loss=avg_loss,
                             loss_std=loss_std,
                             loss_min=loss_min,
                             loss_max=loss_max,
                             val_detailed_loss=val_detailed_loss)

        # 记录验证损失到wandb - 使用val/前缀便于组织
        val_log_dict = {}
        for k, v in val_detailed_loss.items():
            val_log_dict[f"val/{k}"] = v
        val_log_dict["val/total_loss"] = avg_loss
        val_log_dict["val/loss_std"] = loss_std
        val_log_dict["val/loss_min"] = loss_min
        val_log_dict["val/loss_max"] = loss_max

        self.log_dict(val_log_dict, prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size, sync_dist=True)

        # 记录用于checkpoint保存的损失
        self.log('val_loss', avg_loss, prog_bar=True, batch_size=self.batch_size, sync_dist=True)  # ModelCheckpoint监控

        # 记录验证阶段的额外信息
        self.log('val/epoch', float(self.current_epoch), prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size)
        self.log('val/num_batches', float(num_batches), prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size)

        # 如果有学习率调度器，记录当前学习率
        if hasattr(self, 'lr_schedulers') and self.lr_schedulers():
            current_lr = self.lr_schedulers().get_last_lr()[0] if hasattr(self.lr_schedulers(), 'get_last_lr') else self.optimizers().param_groups[0]['lr']
            self.log('val/lr', current_lr, prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size)
        
        self.validation_step_outputs.clear()

    def on_test_start(self):
        self.metric_results = []
        return

    def test_step(self, batch, batch_idx):
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(batch)[:, 0, -1]  # Take last timestep of first sample
        
        pred_dict = build_pred_dict_adaptive(pred_x0)
        batch_size = B * num_grasps if (pred_x0.dim() == 3 and (B, num_grasps, _ := pred_x0.shape)) else pred_x0.shape[0]
        
        metric_dict, metric_details = self.criterion(pred_dict, batch, mode='test')
        self.metric_results.append(metric_details)
        return metric_dict

    def test_step_teaser(self, batch):
        B = self.batch_size
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(batch)
        pred_x0 = pred_x0[:,0,-1]
        
        pred_dict = build_pred_dict_adaptive(pred_x0)
        metric_dict, metric_details = self.criterion.forward_metric(pred_dict, batch)

        return metric_dict, metric_details

    # ========================
    # Optimizer Configuration
    # ========================

    def configure_optimizers(self):
        if self.optimizer_cfg.name.lower() == "adam":
            optimizer = torch.optim.Adam(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        elif self.optimizer_cfg.name.lower() == "adamw":
            optimizer = torch.optim.AdamW(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        else:
            raise NotImplementedError(f"No such optimizer: {self.optimizer_cfg.name}")
        
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            logging.info("Using score model without pretrain, will not load optimizer state")
            self.trainer.fit_loop.epoch_progress.current.completed = 0
            self.last_epoch = -1
        else:
            self.last_epoch = self.current_epoch - 1 if self.current_epoch else -1
        
        logging.info(f"Setting last_epoch to: {self.last_epoch}")
        
        if self.scheduler.name.lower() == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.scheduler.t_max,
                eta_min=self.scheduler.min_lr,
                last_epoch=self.last_epoch
            )
        elif self.scheduler.name.lower() == "steplr":
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                self.scheduler.step_size,
                gamma=self.scheduler.step_gamma,
                last_epoch=self.last_epoch
            )
            
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss"
            }
        }

    # =====================
    # Inference Methods
    # =====================

    def forward_infer(self, data: Dict, k=4, timestep=-1):
        """Inference with k samples at specified timestep."""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, timestep]

        pred_dict = build_pred_dict_adaptive(pred_x0)
        preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
        return preds_hand, targets_hand
    
    def forward_infer_step(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
        """Inference with k samples at multiple timesteps."""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)

        results = []
        for timestep in timesteps:
            pred_x0_t = pred_x0[:, :, timestep]
            pred_dict = build_pred_dict_adaptive(pred_x0_t)
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
            results.append(preds_hand)

        return results

    def forward_get_pose(self, data: Dict, k=4):
        """Get pose predictions."""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        print(f"pred_x0.shape {pred_x0.shape}")

        pred_dict = build_pred_dict_adaptive(pred_x0)
        outputs, targets = self.criterion.infer_norm_process_dict_get_pose(pred_dict, data)
        return outputs, targets

    def forward_get_pose_matched(self, data: Dict, k=4):
        """Get matched pose predictions."""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        print(f"pred_x0.shape {pred_x0.shape}")
        
        pred_dict = build_pred_dict_adaptive(pred_x0)
        matched_pred, matched_targets, outputs, targets = self.criterion.infer_norm_process_dict_get_pose_matched(pred_dict, data)
        return matched_pred, matched_targets, outputs, targets

    def forward_get_pose_raw(self, data: Dict, k=4):
        """Get raw pose predictions."""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_pose_norm = self.sample(data, k=k)
        pred_pose_norm = pred_pose_norm[:, :, -1]
        print(f"pred_pose_norm.shape {pred_pose_norm.shape}")

        hand_model_pose = denorm_hand_pose_robust(pred_pose_norm, self.rot_type, self.mode)
        return hand_model_pose

    def forward_train_instance(self, data: Dict):
        """Forward pass for training a single instance."""
        data = process_hand_pose(data, rot_type=self.rot_type, mode=self.mode)
        B = data['norm_pose'].shape[0]
        
        ts = self._sample_timesteps(B)
        noise = torch.randn_like(data['norm_pose'], device=self.device)
        x_t = self.q_sample(x0=data['norm_pose'], t=ts, noise=noise)

        condition = self.eps_model.condition(data)
        data["cond"] = condition

        output = self.eps_model(x_t, ts, data)

        if self.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            B, *x_shape = x_t.shape
            pred_x0 = self.sqrt_recip_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                        self.sqrt_recipm1_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * output
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }
        outputs, targets = self.criterion.get_hand_model_pose(pred_dict, data)
        outputs, targets = self.criterion.get_hand(outputs, targets)

        return outputs, targets

    # =====================
    # Helper Methods
    # =====================

    def _compute_loss(self, batch: Dict, mode: str = 'train') -> Tuple[torch.Tensor, Dict[str, torch.Tensor], Dict]:
        """Unified loss computation logic for forward_train / training_step."""
        processed_batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)
        norm_pose = processed_batch['norm_pose']
        B = norm_pose.shape[0]

        ts = self._sample_timesteps(B)
        noise = torch.randn_like(norm_pose, device=self.device)
        x_t = self.q_sample(x0=norm_pose, t=ts, noise=noise)

        condition_dict = self.eps_model.condition(processed_batch)
        processed_batch.update(condition_dict)

        output = self.eps_model(x_t, ts, processed_batch)

        if self.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            pred_x0 = self._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }

        if self.use_negative_prompts and 'neg_pred' in condition_dict and condition_dict['neg_pred'] is not None:
            pred_dict['neg_pred'] = condition_dict['neg_pred']
            pred_dict['neg_text_features'] = condition_dict['neg_text_features']

        loss_dict = self.criterion(pred_dict, processed_batch, mode=mode)
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        return loss, loss_dict, processed_batch

    def on_load_checkpoint(self, checkpoint: Dict[str, Any]) -> None:
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            model_state_dict = self.state_dict()
            checkpoint_state_dict = checkpoint['state_dict']
            
            new_state_dict = {}
            for key, value in checkpoint_state_dict.items():
                if 'score_heads' not in key:
                    new_state_dict[key] = value
                    
            model_state_dict.update(new_state_dict)
            self.load_state_dict(model_state_dict)
            logging.info("Loaded checkpoint weights (excluding score_heads)")
        else:
            current_state = self.state_dict()
            different = self._check_state_dict(checkpoint['state_dict'], current_state)
            
            if different:
                logging.warning("State dict inconsistency detected!")
                input("Press ENTER to continue or Ctrl-C to interrupt")
            else:
                logging.info("Checkpoint state dict is CONSISTENT with model state dict")
                
            logging.info("Loading full checkpoint")

    def _check_state_dict(self, dict1, dict2):
        if dict1.keys() != dict2.keys():
            logging.warning("Keys mismatch!")
            logging.warning(f"Only in dict1: {set(dict1.keys()) - set(dict2.keys())}")
            logging.warning(f"Only in dict2: {set(dict2.keys()) - set(dict1.keys())}")
            return True
        
        for key in dict1:
            if dict1[key].shape != dict2[key].shape:
                logging.warning(f"Shape mismatch for {key}!")
                logging.warning(f"Shape in dict1: {dict1[key].shape}")
                logging.warning(f"Shape in dict2: {dict2[key].shape}")
                return True
        
        return False

    # _convert_number_to_emoji 已迁移到 utils.text_utils.convert_number_to_emoji

    # _build_pred_dict_adaptive 方法已移至 models.utils.prediction.build_pred_dict_adaptive
    # 为保持向后兼容，保留同名包装函数，内部调用新实现（后续可彻底移除）。
    def _build_pred_dict_adaptive(self, pred_x0):  # noqa: N802
        """(Deprecated) 向后兼容包装，调用 utils.prediction.build_pred_dict_adaptive"""
        logging.warning("_build_pred_dict_adaptive is deprecated; use build_pred_dict_adaptive from models.utils.prediction instead.")
        return build_pred_dict_adaptive(pred_x0)
